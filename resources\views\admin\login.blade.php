<!DOCTYPE html>
<html lang="th" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - SoloShop Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-body {
            padding: 2rem;
        }

        .btn-login {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            padding: 12px 30px;
            font-weight: 500;
            border-radius: 8px;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #29b6f6, #0288d1);
            transform: translateY(-1px);
        }

        .form-control:focus {
            border-color: #4fc3f7;
            box-shadow: 0 0 0 0.2rem rgba(79, 195, 247, 0.25);
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
        }

        .back-link {
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #4fc3f7;
        }


    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="icon-container">
                    <i class="fas fa-store fa-2x"></i>
                </div>
                <h3 class="mb-0">ผู้ใช้ประจำบริการ</h3>
                <p class="mb-0">เข้าสู่ระบบเพื่อจัดการเว็บไซต์</p>
            </div>

            <div class="login-body">
                @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @foreach($errors->all() as $error)
                    <div>{{ $error }}</div>
                    @endforeach
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
                </div>
                @endif

                <form action="{{ route('admin.login.post') }}" method="POST" id="loginForm">
                    @csrf
                    <div class="form-floating">
                        <input type="email" class="form-control @error('email') is-invalid @enderror"
                               id="email" name="email" value="{{ old('email') }}"
                               placeholder="อีเมล" required autofocus>
                        <label for="email">อีเมล</label>
                        <i class="fas fa-envelope input-icon"></i>
                        @error('email')
                        <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control @error('password') is-invalid @enderror"
                               id="password" name="password" placeholder="รหัสผ่าน" required>
                        <label for="password">รหัสผ่าน</label>
                        <i class="fas fa-lock input-icon"></i>
                        @error('password')
                        <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            จดจำการเข้าสู่ระบบ
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-login" id="loginBtn">
                            <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Demo Account Info -->
        <div class="demo-card">
            <div class="text-center">
                <h6 class="mb-3">
                    <i class="fas fa-info-circle text-primary me-2"></i>ข้อมูลสำหรับทดสอบ
                </h6>
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Email:</span>
                            <code class="bg-light px-2 py-1 rounded"><EMAIL></code>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-medium">Password:</span>
                            <code class="bg-light px-2 py-1 rounded">*********</code>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <a href="{{ route('home') }}" class="back-link">
                <i class="fas fa-arrow-left"></i> กลับสู่หน้าหลัก
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Login Form Interactions
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            // Auto-hide alerts
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.classList.contains('show')) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                });
            }, 5000);

            // Form submission with loading state
            loginForm.addEventListener('submit', function(e) {
                loginBtn.classList.add('loading');
                loginBtn.disabled = true;

                // Re-enable button after 3 seconds (in case of error)
                setTimeout(function() {
                    loginBtn.classList.remove('loading');
                    loginBtn.disabled = false;
                }, 3000);
            });

            // Enhanced input focus effects
            const inputs = [emailInput, passwordInput];
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // Demo credentials quick fill
            const demoEmail = '<EMAIL>';
            const demoPassword = '*********';

            document.addEventListener('keydown', function(e) {
                // Press Ctrl+D to fill demo credentials
                if (e.ctrlKey && e.key === 'd') {
                    e.preventDefault();
                    emailInput.value = demoEmail;
                    passwordInput.value = demoPassword;

                    // Trigger label animations
                    emailInput.dispatchEvent(new Event('input'));
                    passwordInput.dispatchEvent(new Event('input'));

                    // Show notification
                    showNotification('ข้อมูลทดสอบถูกกรอกแล้ว!', 'success');
                }
            });

            // Notification system
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `alert alert-${type} position-fixed`;
                notification.style.cssText = `
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    min-width: 300px;
                    animation: slideInRight 0.3s ease-out;
                `;
                notification.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            // Add slide-in animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
